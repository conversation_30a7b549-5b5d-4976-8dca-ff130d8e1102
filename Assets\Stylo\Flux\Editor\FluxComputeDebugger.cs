using UnityEngine;
using UnityEngine.Rendering;
using UnityEngine.Rendering.Universal;
using System.Collections.Generic;

#if UNITY_EDITOR
using UnityEditor;
#endif

namespace Stylo.Flux.Universal
{
    /// <summary>
    /// Debugging tools specifically for compute shader development and validation.
    /// Provides texture visualization, compilation validation, and side-by-side comparison tools.
    /// </summary>
    public class FluxComputeDebugger : MonoBehaviour
    {
        [Header("Debug Settings")]
        [SerializeField] private bool enableDebugging = true;
        [SerializeField] private bool showDebugTextures = false;
        [SerializeField] private bool validateComputeShaders = true;
        [SerializeField] private bool compareWithFragment = false;

        [Header("Visualization")]
        [SerializeField] private DebugVisualizationMode visualizationMode = DebugVisualizationMode.Off;

        [Header("Comparison Settings")]
        [SerializeField] private float pixelToleranceThreshold = 0.01f;
        [SerializeField] private bool showDifferenceMap = false;
        [SerializeField] private bool logPixelDifferences = false;

        public enum DebugVisualizationMode
        {
            Off,
            MotionVectors,
            CompressionArtifacts,
            UAVOutputs,
            IntermediateResults,
            ThreadGroupVisualization,
            PerformanceHeatmap
        }

        // Debug textures and resources
        private RenderTexture? debugMotionVectorTexture;
        private RenderTexture? debugCompressionTexture;
        private RenderTexture? debugUAVTexture;
        private RenderTexture? fragmentReferenceTexture;
        private RenderTexture? computeResultTexture;
        private RenderTexture? differenceMapTexture;

        // Compute shader validation
        private ComputeShader? targetComputeShader;
        private List<string> compilationErrors = new List<string>();
        private List<string> validationWarnings = new List<string>();

        // Performance tracking
        private Dictionary<string, float> computeTimings = new Dictionary<string, float>();
        private Dictionary<string, float> fragmentTimings = new Dictionary<string, float>();

        // Debug GUI
        private bool showDebugGUI = false;
        private Rect debugWindowRect = new Rect(10, 10, 400, 600);

        private void Start()
        {
            if (enableDebugging)
            {
                InitializeDebugResources();

                if (validateComputeShaders)
                {
                    ValidateComputeShaderCompilation();
                }
            }
        }

        private void InitializeDebugResources()
        {
            int screenWidth = Screen.width;
            int screenHeight = Screen.height;

            // Create debug textures
            debugMotionVectorTexture = CreateDebugTexture(screenWidth, screenHeight, "Flux Debug Motion Vectors");
            debugCompressionTexture = CreateDebugTexture(screenWidth, screenHeight, "Flux Debug Compression");
            debugUAVTexture = CreateDebugTexture(screenWidth, screenHeight, "Flux Debug UAV");

            if (compareWithFragment)
            {
                fragmentReferenceTexture = CreateDebugTexture(screenWidth, screenHeight, "Flux Fragment Reference");
                computeResultTexture = CreateDebugTexture(screenWidth, screenHeight, "Flux Compute Result");
                differenceMapTexture = CreateDebugTexture(screenWidth, screenHeight, "Flux Difference Map");
            }
        }

        private RenderTexture CreateDebugTexture(int width, int height, string name)
        {
            var rt = new RenderTexture(width, height, 0, RenderTextureFormat.ARGBFloat);
            rt.name = name;
            rt.enableRandomWrite = true;
            rt.Create();
            return rt;
        }

        private void ValidateComputeShaderCompilation()
        {
            compilationErrors.Clear();
            validationWarnings.Clear();

            // Find compute shader
            targetComputeShader = Resources.Load<ComputeShader>("Stylo/Flux/Shaders/FluxCompute");

            if (targetComputeShader == null)
            {
                compilationErrors.Add("FluxCompute shader not found in Resources folder");
                return;
            }

            // Validate kernel availability
            string[] expectedKernels = {
                "CSEncodeMain",
                "CSDecodeMain",
                "CSEncodeMainMobile",
                "CSDecodeMainMobile",
                "CSDebugMotionVectors",
                "CSDebugCompression"
            };

            foreach (string kernelName in expectedKernels)
            {
                try
                {
                    int kernelId = targetComputeShader.FindKernel(kernelName);
                    Debug.Log($"[FluxDebugger] Kernel '{kernelName}' found with ID: {kernelId}");
                }
                catch (System.Exception e)
                {
                    compilationErrors.Add($"Kernel '{kernelName}' not found: {e.Message}");
                }
            }

            // Validate platform support
            if (!SystemInfo.supportsComputeShaders)
            {
                validationWarnings.Add("Compute shaders not supported on this platform");
            }

            // Check thread group limits
            int maxThreadGroupSize = SystemInfo.maxComputeWorkGroupSize;
            if (maxThreadGroupSize < 256) // 16x16 thread groups
            {
                validationWarnings.Add($"Maximum thread group size ({maxThreadGroupSize}) may limit performance");
            }

            // Log results
            if (compilationErrors.Count > 0)
            {
                Debug.LogError($"[FluxDebugger] Compute shader validation failed with {compilationErrors.Count} errors");
                foreach (string error in compilationErrors)
                {
                    Debug.LogError($"[FluxDebugger] Error: {error}");
                }
            }

            if (validationWarnings.Count > 0)
            {
                Debug.LogWarning($"[FluxDebugger] {validationWarnings.Count} validation warnings");
                foreach (string warning in validationWarnings)
                {
                    Debug.LogWarning($"[FluxDebugger] Warning: {warning}");
                }
            }

            if (compilationErrors.Count == 0 && validationWarnings.Count == 0)
            {
                Debug.Log("[FluxDebugger] Compute shader validation passed successfully");
            }
        }

        public void CaptureDebugVisualization(FluxComputePass computePass, int debugMode)
        {
            if (!enableDebugging || visualizationMode == DebugVisualizationMode.Off) return;

            CommandBuffer cmd = CommandBufferPool.Get("Flux Debug Visualization");

            try
            {
                switch (visualizationMode)
                {
                    case DebugVisualizationMode.MotionVectors:
                        computePass.ExecuteDebugPass(cmd, UnityEngine.Rendering.RTHandles.Alloc(debugMotionVectorTexture), 0);
                        break;

                    case DebugVisualizationMode.CompressionArtifacts:
                        computePass.ExecuteDebugPass(cmd, UnityEngine.Rendering.RTHandles.Alloc(debugCompressionTexture), 1);
                        break;

                    case DebugVisualizationMode.ThreadGroupVisualization:
                        VisualizeThreadGroups(cmd, computePass);
                        break;
                }

                Graphics.ExecuteCommandBuffer(cmd);
            }
            finally
            {
                CommandBufferPool.Release(cmd);
            }
        }

        private void VisualizeThreadGroups(CommandBuffer cmd, FluxComputePass computePass)
        {
            // Create a visualization showing thread group boundaries and utilization
            // This would involve creating a special debug kernel that colors pixels based on thread group ID
            var qualitySettings = computePass.GetQualitySettings();
            int threadGroupSize = qualitySettings.threadGroupSize;

            // For now, just log the thread group configuration
            Debug.Log($"[FluxDebugger] Thread Group Size: {threadGroupSize}x{threadGroupSize}");
            Debug.Log($"[FluxDebugger] Block Size: {qualitySettings.blockSize}");
        }

        public void CompareComputeWithFragment(RenderTexture fragmentResult, RenderTexture computeResult)
        {
            if (!compareWithFragment || fragmentResult == null || computeResult == null) return;

            // Copy textures for comparison
            Graphics.Blit(fragmentResult, fragmentReferenceTexture);
            Graphics.Blit(computeResult, computeResultTexture);

            // Generate difference map
            GenerateDifferenceMap();

            // Analyze pixel differences
            AnalyzePixelDifferences();
        }

        private void GenerateDifferenceMap()
        {
            // Create a material for difference calculation
            Material diffMaterial = new Material(Shader.Find("Hidden/FluxComputeDebugger/Difference"));
            if (diffMaterial == null)
            {
                Debug.LogError("[FluxDebugger] Difference shader not found");
                return;
            }

            diffMaterial.SetTexture("_FragmentTex", fragmentReferenceTexture);
            diffMaterial.SetTexture("_ComputeTex", computeResultTexture);
            diffMaterial.SetFloat("_Tolerance", pixelToleranceThreshold);

            Graphics.Blit(null, differenceMapTexture, diffMaterial);

            if (showDifferenceMap)
            {
                // Display difference map on screen
                Graphics.Blit(differenceMapTexture, (RenderTexture?)null);
            }

            Object.DestroyImmediate(diffMaterial);
        }

        private void AnalyzePixelDifferences()
        {
            if (!logPixelDifferences) return;

            // This would require compute shader or texture readback to analyze differences
            // For now, provide a simplified analysis
            Debug.Log("[FluxDebugger] Pixel difference analysis completed. Check difference map texture for details.");
        }

        public void RecordComputeTiming(string passName, float timeMs)
        {
            if (!enableDebugging) return;
            computeTimings[passName] = timeMs;
        }

        public void RecordFragmentTiming(string passName, float timeMs)
        {
            if (!enableDebugging) return;
            fragmentTimings[passName] = timeMs;
        }

        public void ValidateDispatchParameters(Vector3Int threadGroups, Vector3Int threadGroupSize)
        {
            if (!enableDebugging) return;

            // Check for common dispatch issues
            if (threadGroups.x <= 0 || threadGroups.y <= 0 || threadGroups.z <= 0)
            {
                Debug.LogError($"[FluxDebugger] Invalid thread group count: {threadGroups}");
            }

            if (threadGroupSize.x * threadGroupSize.y * threadGroupSize.z > SystemInfo.maxComputeWorkGroupSize)
            {
                Debug.LogError($"[FluxDebugger] Thread group size ({threadGroupSize.x}x{threadGroupSize.y}x{threadGroupSize.z}) exceeds platform limit ({SystemInfo.maxComputeWorkGroupSize})");
            }

            // Check for optimal thread group sizes
            int totalThreads = threadGroupSize.x * threadGroupSize.y * threadGroupSize.z;
            if (totalThreads % 32 != 0) // Warp size on most GPUs
            {
                validationWarnings.Add($"Thread group size ({totalThreads}) is not a multiple of 32, may cause inefficiency");
            }
        }

        private void Update()
        {
            if (Input.GetKeyDown(KeyCode.F12))
            {
                showDebugGUI = !showDebugGUI;
            }
        }

        private void OnGUI()
        {
            if (!showDebugGUI || !enableDebugging) return;

            debugWindowRect = GUILayout.Window(0, debugWindowRect, DebugWindow, "Flux Compute Debugger");
        }

        private void DebugWindow(int windowID)
        {
            GUILayout.Label("Compute Shader Debug", EditorStyles.boldLabel);

            // Visualization controls
            GUILayout.Label("Visualization Mode:");
            visualizationMode = (DebugVisualizationMode)GUILayout.SelectionGrid(
                (int)visualizationMode,
                System.Enum.GetNames(typeof(DebugVisualizationMode)),
                2
            );

            GUILayout.Space(10);

            // Debug settings
            showDebugTextures = GUILayout.Toggle(showDebugTextures, "Show Debug Textures");
            compareWithFragment = GUILayout.Toggle(compareWithFragment, "Compare with Fragment");
            showDifferenceMap = GUILayout.Toggle(showDifferenceMap, "Show Difference Map");

            GUILayout.Space(10);

            // Performance timings
            if (computeTimings.Count > 0 || fragmentTimings.Count > 0)
            {
                GUILayout.Label("Performance Timings:", EditorStyles.boldLabel);

                foreach (var timing in computeTimings)
                {
                    GUILayout.Label($"Compute {timing.Key}: {timing.Value:F2}ms");
                }

                foreach (var timing in fragmentTimings)
                {
                    GUILayout.Label($"Fragment {timing.Key}: {timing.Value:F2}ms");
                }
            }

            GUILayout.Space(10);

            // Validation results
            if (compilationErrors.Count > 0)
            {
                GUILayout.Label("Compilation Errors:", EditorStyles.boldLabel);
                foreach (string error in compilationErrors)
                {
                    GUILayout.Label(error, EditorStyles.helpBox);
                }
            }

            if (validationWarnings.Count > 0)
            {
                GUILayout.Label("Warnings:", EditorStyles.boldLabel);
                foreach (string warning in validationWarnings)
                {
                    GUILayout.Label(warning, EditorStyles.helpBox);
                }
            }

            GUI.DragWindow();
        }

        private void OnDestroy()
        {
            // Clean up debug textures
            if (debugMotionVectorTexture != null) debugMotionVectorTexture.Release();
            if (debugCompressionTexture != null) debugCompressionTexture.Release();
            if (debugUAVTexture != null) debugUAVTexture.Release();
            if (fragmentReferenceTexture != null) fragmentReferenceTexture.Release();
            if (computeResultTexture != null) computeResultTexture.Release();
            if (differenceMapTexture != null) differenceMapTexture.Release();
        }

        // Public API for external debugging
        public bool HasCompilationErrors => compilationErrors.Count > 0;
        public List<string> GetCompilationErrors() => new List<string>(compilationErrors);
        public List<string> GetValidationWarnings() => new List<string>(validationWarnings);
        public Dictionary<string, float> GetComputeTimings() => new Dictionary<string, float>(computeTimings);
        public Dictionary<string, float> GetFragmentTimings() => new Dictionary<string, float>(fragmentTimings);
    }
}
