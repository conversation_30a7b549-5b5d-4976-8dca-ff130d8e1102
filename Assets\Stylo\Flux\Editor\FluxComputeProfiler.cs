using UnityEngine;
using UnityEngine.Rendering;
using UnityEngine.Profiling;
using System.Collections.Generic;
using System.Linq;

#if UNITY_EDITOR
using UnityEditor;
#endif

namespace Stylo.Flux.Universal
{
    /// <summary>
    /// Comprehensive profiling tool for analyzing compute shader performance versus fragment shader performance.
    /// Provides detailed GPU timing, memory bandwidth analysis, and automated benchmarking.
    /// </summary>
    public class FluxComputeProfiler : MonoBehaviour
    {
        [Header("Profiling Settings")]
        [SerializeField] private bool enableProfiling = true;
        [SerializeField] private int sampleFrameCount = 60;
        [SerializeField] private bool autoRunBenchmarks = false;
        [SerializeField] private float benchmarkDuration = 5.0f;

        [Header("Performance Targets")]
        [SerializeField] private float targetFrameTime = 16.67f; // 60 FPS
        [SerializeField] private float acceptableFrameTime = 33.33f; // 30 FPS

        // Performance data storage
        private Queue<PerformanceFrame> fragmentShaderFrames = new Queue<PerformanceFrame>();
        private Queue<PerformanceFrame> computeShaderFrames = new Queue<PerformanceFrame>();
        private Queue<MemoryFrame> memoryFrames = new Queue<MemoryFrame>();

        // Profiling state
        private bool isProfilingFragment = false;
        private bool isProfilingCompute = false;

        // GPU timing
        private CommandBuffer? profilingCommandBuffer;
        private readonly List<string> profilingMarkers = new List<string>
        {
            "Flux Downscale",
            "Flux Encode",
            "Flux Decode",
            "Flux Upscale",
            "Flux CopyToPrev"
        };

        // Benchmark results
        private BenchmarkResults lastBenchmarkResults;

        public struct PerformanceFrame
        {
            public float totalTime;
            public float downscaleTime;
            public float encodeTime;
            public float decodeTime;
            public float upscaleTime;
            public float copyTime;
            public int dispatchCount;
            public float frameTime;
            public int frameNumber;
        }

        public struct MemoryFrame
        {
            public long allocatedMemory;
            public long reservedMemory;
            public long monoMemory;
            public float memoryBandwidth;
            public int textureMemory;
            public int frameNumber;
        }

        public struct BenchmarkResults
        {
            public float fragmentAvgFrameTime;
            public float computeAvgFrameTime;
            public float performanceImprovement;
            public float fragmentAvgGPUTime;
            public float computeAvgGPUTime;
            public float gpuTimeImprovement;
            public float avgMemoryBandwidth;
            public string platformInfo;
            public string gpuInfo;
            public bool computeRecommended;
        }

        private void Start()
        {
            if (enableProfiling)
            {
                InitializeProfiling();

                if (autoRunBenchmarks)
                {
                    StartCoroutine(RunAutomatedBenchmark());
                }
            }
        }

        private void InitializeProfiling()
        {
            profilingCommandBuffer = new CommandBuffer();
            profilingCommandBuffer.name = "Flux Profiling";

            // Register for render pipeline events
            RenderPipelineManager.beginCameraRendering += OnBeginCameraRendering;
            RenderPipelineManager.endCameraRendering += OnEndCameraRendering;
        }

        private void OnDestroy()
        {
            if (profilingCommandBuffer != null)
            {
                profilingCommandBuffer.Release();
                profilingCommandBuffer = null;
            }

            RenderPipelineManager.beginCameraRendering -= OnBeginCameraRendering;
            RenderPipelineManager.endCameraRendering -= OnEndCameraRendering;
        }

        private void OnBeginCameraRendering(ScriptableRenderContext context, Camera camera)
        {
            if (!enableProfiling || camera.cameraType != CameraType.Game) return;

            // Begin GPU timing
            foreach (var marker in profilingMarkers)
            {
                profilingCommandBuffer.BeginSample(marker);
            }

            context.ExecuteCommandBuffer(profilingCommandBuffer);
            profilingCommandBuffer.Clear();
        }

        private void OnEndCameraRendering(ScriptableRenderContext context, Camera camera)
        {
            if (!enableProfiling || camera.cameraType != CameraType.Game) return;

            // End GPU timing
            foreach (var marker in profilingMarkers)
            {
                profilingCommandBuffer.EndSample(marker);
            }

            context.ExecuteCommandBuffer(profilingCommandBuffer);
            profilingCommandBuffer.Clear();

            // Record performance data
            RecordPerformanceFrame();
        }

        private void RecordPerformanceFrame()
        {
            float frameTime = Time.unscaledDeltaTime * 1000f; // Convert to milliseconds

            // Create performance frame data
            var performanceFrame = new PerformanceFrame
            {
                frameTime = frameTime,
                frameNumber = Time.frameCount,
                totalTime = GetGPUTime("Flux"),
                downscaleTime = GetGPUTime("Flux Downscale"),
                encodeTime = GetGPUTime("Flux Encode"),
                decodeTime = GetGPUTime("Flux Decode"),
                upscaleTime = GetGPUTime("Flux Upscale"),
                copyTime = GetGPUTime("Flux CopyToPrev"),
                dispatchCount = 5 // Default for 5-pass pipeline
            };

            // Store in appropriate queue based on current profiling mode
            if (isProfilingFragment)
            {
                fragmentShaderFrames.Enqueue(performanceFrame);
                if (fragmentShaderFrames.Count > sampleFrameCount)
                    fragmentShaderFrames.Dequeue();
            }
            else if (isProfilingCompute)
            {
                computeShaderFrames.Enqueue(performanceFrame);
                if (computeShaderFrames.Count > sampleFrameCount)
                    computeShaderFrames.Dequeue();
            }

            // Record memory data
            RecordMemoryFrame();
        }

        private void RecordMemoryFrame()
        {
            var memoryFrame = new MemoryFrame
            {
                allocatedMemory = Profiler.GetTotalAllocatedMemoryLong(),
                reservedMemory = Profiler.GetTotalReservedMemoryLong(),
                monoMemory = Profiler.GetMonoUsedSizeLong(),
                memoryBandwidth = CalculateMemoryBandwidth(),
                textureMemory = (int)Profiler.GetAllocatedMemoryForGraphicsDriver(),
                frameNumber = Time.frameCount
            };

            memoryFrames.Enqueue(memoryFrame);
            if (memoryFrames.Count > sampleFrameCount)
                memoryFrames.Dequeue();
        }

        private float GetGPUTime(string markerName)
        {
            // In a real implementation, this would use Unity's Profiler API
            // to get actual GPU timing data from the profiling markers
            return 0f; // Placeholder
        }

        private float CalculateMemoryBandwidth()
        {
            // Estimate memory bandwidth based on texture uploads and GPU memory usage
            // This is a simplified calculation
            float textureMemoryMB = Profiler.GetAllocatedMemoryForGraphicsDriver() / (1024f * 1024f);
            float bandwidth = textureMemoryMB * 60f; // Rough estimate: MB/s at 60fps
            return bandwidth;
        }

        public void StartFragmentShaderProfiling()
        {
            isProfilingFragment = true;
            isProfilingCompute = false;
            fragmentShaderFrames.Clear();
            Debug.Log("[FluxProfiler] Started fragment shader profiling");
        }

        public void StartComputeShaderProfiling()
        {
            isProfilingFragment = false;
            isProfilingCompute = true;
            computeShaderFrames.Clear();
            Debug.Log("[FluxProfiler] Started compute shader profiling");
        }

        public void StopProfiling()
        {
            isProfilingFragment = false;
            isProfilingCompute = false;
            Debug.Log("[FluxProfiler] Stopped profiling");
        }

        public BenchmarkResults GenerateComparison()
        {
            if (fragmentShaderFrames.Count == 0 || computeShaderFrames.Count == 0)
            {
                Debug.LogWarning("[FluxProfiler] Insufficient data for comparison. Run both fragment and compute shader profiling first.");
                return default;
            }

            var fragmentData = fragmentShaderFrames.ToArray();
            var computeData = computeShaderFrames.ToArray();

            float fragmentAvgFrameTime = fragmentData.Average(f => f.frameTime);
            float computeAvgFrameTime = computeData.Average(f => f.frameTime);

            float fragmentAvgGPUTime = fragmentData.Average(f => f.totalTime);
            float computeAvgGPUTime = computeData.Average(f => f.totalTime);

            float memoryBandwidth = memoryFrames.Count > 0 ? memoryFrames.Average(m => m.memoryBandwidth) : 0f;

            var results = new BenchmarkResults
            {
                fragmentAvgFrameTime = fragmentAvgFrameTime,
                computeAvgFrameTime = computeAvgFrameTime,
                performanceImprovement = ((fragmentAvgFrameTime - computeAvgFrameTime) / fragmentAvgFrameTime) * 100f,
                fragmentAvgGPUTime = fragmentAvgGPUTime,
                computeAvgGPUTime = computeAvgGPUTime,
                gpuTimeImprovement = ((fragmentAvgGPUTime - computeAvgGPUTime) / fragmentAvgGPUTime) * 100f,
                avgMemoryBandwidth = memoryBandwidth,
                platformInfo = GetPlatformInfo(),
                gpuInfo = SystemInfo.graphicsDeviceName,
                computeRecommended = computeAvgFrameTime < fragmentAvgFrameTime
            };

            lastBenchmarkResults = results;
            return results;
        }

        private string GetPlatformInfo()
        {
            return $"{SystemInfo.operatingSystem} | {SystemInfo.processorType} | {SystemInfo.systemMemorySize}MB RAM";
        }

        private System.Collections.IEnumerator RunAutomatedBenchmark()
        {
            Debug.Log("[FluxProfiler] Starting automated benchmark");

            // Phase 1: Fragment shader profiling
            StartFragmentShaderProfiling();
            yield return new WaitForSeconds(benchmarkDuration);

            // Phase 2: Compute shader profiling
            StartComputeShaderProfiling();
            yield return new WaitForSeconds(benchmarkDuration);

            // Phase 3: Generate results
            StopProfiling();
            var results = GenerateComparison();

            Debug.Log($"[FluxProfiler] Benchmark complete. Performance improvement: {results.performanceImprovement:F1}%");
            Debug.Log($"[FluxProfiler] Fragment avg: {results.fragmentAvgFrameTime:F2}ms, Compute avg: {results.computeAvgFrameTime:F2}ms");
            Debug.Log($"[FluxProfiler] Compute shaders recommended: {results.computeRecommended}");
        }

        public void ExportResults(string? filePath = null)
        {
            if (lastBenchmarkResults.Equals(default(BenchmarkResults)))
            {
                Debug.LogWarning("[FluxProfiler] No benchmark results to export");
                return;
            }

            if (string.IsNullOrEmpty(filePath))
            {
                filePath = Application.persistentDataPath + "/flux_benchmark_results.json";
            }

            try
            {
                string json = JsonUtility.ToJson(lastBenchmarkResults, true);
                System.IO.File.WriteAllText(filePath, json);
                Debug.Log($"[FluxProfiler] Results exported to: {filePath}");
            }
            catch (System.Exception e)
            {
                Debug.LogError($"[FluxProfiler] Failed to export results: {e.Message}");
            }
        }

#if UNITY_EDITOR
        public void ShowResultsWindow()
        {
            FluxComputeProfilerWindow.ShowWindow(lastBenchmarkResults);
        }
#endif

        // Public getters for external analysis
        public PerformanceFrame[] GetFragmentShaderData() => fragmentShaderFrames.ToArray();
        public PerformanceFrame[] GetComputeShaderData() => computeShaderFrames.ToArray();
        public MemoryFrame[] GetMemoryData() => memoryFrames.ToArray();
        public BenchmarkResults GetLastResults() => lastBenchmarkResults;
    }

#if UNITY_EDITOR
    /// <summary>
    /// Editor window for displaying profiling results
    /// </summary>
    public class FluxComputeProfilerWindow : EditorWindow
    {
        private FluxComputeProfiler.BenchmarkResults results;

        public static void ShowWindow(FluxComputeProfiler.BenchmarkResults results)
        {
            var window = GetWindow<FluxComputeProfilerWindow>("Flux Compute Profiler");
            window.results = results;
            window.Show();
        }

        private void OnGUI()
        {
            EditorGUILayout.LabelField("Flux Compute Shader Performance Analysis", EditorStyles.boldLabel);
            EditorGUILayout.Space();

            if (results.Equals(default(FluxComputeProfiler.BenchmarkResults)))
            {
                EditorGUILayout.HelpBox("No benchmark data available. Run profiling first.", MessageType.Info);
                return;
            }

            EditorGUILayout.LabelField("Platform Information", EditorStyles.boldLabel);
            EditorGUILayout.LabelField("System:", results.platformInfo);
            EditorGUILayout.LabelField("GPU:", results.gpuInfo);
            EditorGUILayout.Space();

            EditorGUILayout.LabelField("Performance Results", EditorStyles.boldLabel);
            EditorGUILayout.LabelField($"Fragment Shader Avg Frame Time: {results.fragmentAvgFrameTime:F2}ms");
            EditorGUILayout.LabelField($"Compute Shader Avg Frame Time: {results.computeAvgFrameTime:F2}ms");
            EditorGUILayout.LabelField($"Performance Improvement: {results.performanceImprovement:F1}%");
            EditorGUILayout.Space();

            EditorGUILayout.LabelField($"Fragment Shader Avg GPU Time: {results.fragmentAvgGPUTime:F2}ms");
            EditorGUILayout.LabelField($"Compute Shader Avg GPU Time: {results.computeAvgGPUTime:F2}ms");
            EditorGUILayout.LabelField($"GPU Time Improvement: {results.gpuTimeImprovement:F1}%");
            EditorGUILayout.Space();

            EditorGUILayout.LabelField($"Average Memory Bandwidth: {results.avgMemoryBandwidth:F1} MB/s");
            EditorGUILayout.Space();

            string recommendation = results.computeRecommended ? "Compute shaders recommended" : "Fragment shaders recommended";
            MessageType messageType = results.computeRecommended ? MessageType.Info : MessageType.Warning;
            EditorGUILayout.HelpBox(recommendation, messageType);
        }
    }
#endif
}
